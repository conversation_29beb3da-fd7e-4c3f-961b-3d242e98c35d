from fastapi import APIRouter, HTTPException, Depends
from fastapi.responses import StreamingResponse
from typing import Dict, List, Optional, AsyncGenerator
from langchain_ollama import ChatOllama
from langchain_core.messages import AIMessage
from langchain_openai import Chat<PERSON>penAI
from langchain_groq import <PERSON>tG<PERSON>q
from dotenv import load_dotenv
import os
import json
import asyncio
from logging_config import setup_logger
from pydantic import BaseModel
from sqlalchemy.orm import Session
from .system_prompt import SYSTEM_PROMPT_CONTENT
from .email_service import send_contact_email
from database.connection import get_db
from database import crud, models
# Load .env file
load_dotenv()
logger = setup_logger("routes.api")
router = APIRouter(prefix="/api", tags=["portfolio"])

# Sample data models/schemas
class Project(BaseModel):
    id: int
    title: str
    description: str
    technologies: List[str]
    category: str
    github_url: Optional[str] = None
    live_url: Optional[str] = None
    date: str
    image_url: Optional[str] = None

class Skill(BaseModel):
    name: str
    level: int  # 1-100
    category: str

class Experience(BaseModel):
    id: int
    company: str
    position: str
    start_date: str
    end_date: Optional[str] = None
    description: str
    technologies: List[str]
    achievements: Optional[List[str]] = None
    logo: Optional[str] = None
    company_url: Optional[str] = None
    location: Optional[str] = None

class Education(BaseModel):
    id: int
    institution: str
    degree: str
    field: str
    start_date: str
    end_date: Optional[str] = None
    description: Optional[str] = None
    achievements: Optional[List[str]] = None
    logo: Optional[str] = None
    institution_url: Optional[str] = None
    location: Optional[str] = None

class Certification(BaseModel):
    id: int
    title: str
    issuer: str
    date: str
    icon: str
    verification_url: Optional[str] = None

class Publication(BaseModel):
    id: int
    title: str
    authors: str
    journal: str
    year: str
    volume: Optional[str] = None
    issue: Optional[str] = None
    pages: Optional[str] = None
    doi: Optional[str] = None
    abstract: str
    keywords: List[str]
    pdf_link: Optional[str] = None
    external_link: Optional[str] = None
    category: str
    featured: bool = False
    published: bool = True

class BlogPost(BaseModel):
    id: int
    title: str
    excerpt: str
    date: str
    author: str
    category: str
    tags: List[str]
    image: Optional[str] = None
    featured: bool = False
    content: Optional[str] = None

class ContactMessage(BaseModel):
    name: str
    email: str
    subject: str
    message: str

class ChatMessage(BaseModel):
    message: str



#### Ollama Model #################################################
# MODEL_NAME = "mistral:7b"
# llm = ChatOllama(
#     base_url = "http://ollama:11434/",
#     model=MODEL_NAME,
#     temperature=4
# )
#####################################################



#### Docker Model Runner #################################################
# MODEL_NAME = "ai/llama3.2:3B-Q4_K_M"

# llm = ChatOpenAI(
#         model=MODEL_NAME,
#         base_url="http://host.docker.internal:12434/engines/v1",
#         api_key="ignored"
#     )

#####################################################


# #### OpenAI Model #################################################
# MODEL_NAME = "gpt-4.1"

# llm = ChatOpenAI(model=MODEL_NAME, temperature=0.7)

# #####################################################


#### Groq API Model #################################################
MODEL_NAME = "meta-llama/llama-4-scout-17b-16e-instruct"

llm = ChatGroq(model=MODEL_NAME, temperature=0.7)

#####################################################


def llm_response(user_message: str) -> AIMessage:
    messages = [("system",SYSTEM_PROMPT_CONTENT,),("human", user_message),]
    ai_msg = llm.invoke(messages)
    return ai_msg.content

async def llm_stream_response(user_message: str) -> AsyncGenerator[str, None]:
    """Stream response from LLM word by word"""
    messages = [("system", SYSTEM_PROMPT_CONTENT), ("human", user_message)]

    try:
        # Get the full response first
        ai_msg = llm.invoke(messages)
        response_text = ai_msg.content

        # Split response into words and stream them
        words = response_text.split()
        for i, word in enumerate(words):
            # Add space before word (except for first word)
            if i > 0:
                yield " "
            yield word
            # Add a small delay to simulate streaming
            await asyncio.sleep(0.05)  # 50ms delay between words

    except Exception as e:
        yield f"Error: {str(e)}"


# API Routes
@router.get("/")
def read_root():
    logger.info("Rahul Bhoyar Website - Root endpoint accessed")
    return {"message": "Welcome to Rahul Bhoyar's Portfolio API"}

@router.get("/health")
def health_check(db: Session = Depends(get_db)):
    """Health check endpoint to verify database connectivity."""
    try:
        # Test database connection by counting skills
        skills_count = len(crud.get_skills(db))
        projects_count = len(crud.get_projects(db))

        return {
            "status": "healthy",
            "database": "connected",
            "data_counts": {
                "skills": skills_count,
                "projects": projects_count
            },
            "message": "Portfolio API is running with database connectivity"
        }
    except Exception as e:
        return {
            "status": "unhealthy",
            "database": "disconnected",
            "error": str(e),
            "message": "Database connection failed"
        }

@router.get("/projects", response_model=List[Project])
def get_projects_endpoint(db: Session = Depends(get_db)):
    logger.info("Project Endpoint accessed")
    db_projects = crud.get_projects(db)
    return [Project(
        id=p.id,
        title=p.title,
        description=p.description,
        technologies=p.technologies,
        category=p.category,
        github_url=p.github_url,
        live_url=p.live_url,
        date=p.date,
        image_url=p.image_url
    ) for p in db_projects]

@router.get("/projects/{project_id}", response_model=Project)
def get_project_endpoint(project_id: int, db: Session = Depends(get_db)):
    db_project = crud.get_project_by_id(db, project_id)
    if not db_project:
        raise HTTPException(status_code=404, detail="Project not found")
    return Project(
        id=db_project.id,
        title=db_project.title,
        description=db_project.description,
        technologies=db_project.technologies,
        category=db_project.category,
        github_url=db_project.github_url,
        live_url=db_project.live_url,
        date=db_project.date,
        image_url=db_project.image_url
    )

@router.get("/skills", response_model=List[Skill])
def get_skills_endpoint(db: Session = Depends(get_db)):
    logger.info("Skills Endpoint accessed")
    db_skills = crud.get_skills(db)
    return [Skill(
        name=s.name,
        level=s.level,
        category=s.category
    ) for s in db_skills]

@router.get("/experience", response_model=List[Experience])
def get_experience_endpoint(db: Session = Depends(get_db)):
    logger.info("Experience Endpoint accessed")
    db_experiences = crud.get_experiences(db)
    return [Experience(
        id=e.id,
        company=e.company,
        position=e.position,
        start_date=e.start_date,
        end_date=e.end_date,
        description=e.description,
        technologies=e.technologies,
        achievements=e.achievements,
        logo=e.logo,
        company_url=e.company_url,
        location=e.location
    ) for e in db_experiences]

@router.get("/education", response_model=List[Education])
def get_education_endpoint(db: Session = Depends(get_db)):
    logger.info("Education Endpoint accessed")
    db_education = crud.get_education(db)
    return [Education(
        id=e.id,
        degree=e.degree,
        field=e.field,
        institution=e.institution,
        start_date=e.start_date,
        end_date=e.end_date,
        description=e.description,
        achievements=e.achievements,
        logo=e.logo,
        institution_url=e.institution_url,
        location=e.location
    ) for e in db_education]

@router.get("/certifications", response_model=List[Certification])
def get_certifications_endpoint(db: Session = Depends(get_db)):
    logger.info("Certifications Endpoint accessed")
    db_certifications = crud.get_certifications(db)
    return [Certification(
        id=c.id,
        title=c.title,
        issuer=c.issuer,
        date=c.date,
        icon=c.icon,
        verification_url=c.verification_url
    ) for c in db_certifications]

@router.get("/publications", response_model=List[Publication])
def get_publications_endpoint(db: Session = Depends(get_db)):
    logger.info("Publications Endpoint accessed")
    db_publications = crud.get_publications(db)
    return [Publication(
        id=p.id,
        title=p.title,
        authors=p.authors,
        journal=p.journal,
        year=p.year,
        volume=p.volume,
        issue=p.issue,
        pages=p.pages,
        doi=p.doi,
        abstract=p.abstract,
        keywords=p.keywords,
        pdf_link=p.pdf_link,
        external_link=p.external_link,
        category=p.category,
        featured=p.featured,
        published=getattr(p, 'published', p.abstract != "TBD")  # Default logic for existing data
    ) for p in db_publications]

@router.get("/blog", response_model=List[BlogPost])
def get_blog_posts_endpoint(db: Session = Depends(get_db)):
    db_blog_posts = crud.get_blog_posts(db)
    return [BlogPost(
        id=b.id,
        title=b.title,
        excerpt=b.excerpt,
        date=b.date,
        author=b.author,
        category=b.category,
        tags=b.tags,
        image=b.image,
        featured=b.featured,
        content=b.content
    ) for b in db_blog_posts]

@router.get("/blog/{post_id}", response_model=BlogPost)
def get_blog_post_endpoint(post_id: int, db: Session = Depends(get_db)):
    db_blog_posts = crud.get_blog_posts(db)
    for post in db_blog_posts:
        if post.id == post_id:
            return BlogPost(
                id=post.id,
                title=post.title,
                excerpt=post.excerpt,
                date=post.date,
                author=post.author,
                category=post.category,
                tags=post.tags,
                image=post.image,
                featured=post.featured,
                content=post.content
            )
    raise HTTPException(status_code=404, detail="Blog post not found")

@router.post("/contact", status_code=201)
def send_message(message: ContactMessage, db: Session = Depends(get_db)):
    """Handle contact form submission and send email"""
    logger.info("Contact Endpoint accessed")
    try:
        # Store message in database
        db_message = crud.create_contact_message(
            db=db,
            name=message.name,
            email=message.email,
            subject=message.subject,
            message=message.message
        )

        # Send email using the email service
        send_contact_email(
            name=message.name,
            email=message.email,
            subject=message.subject,
            message=message.message
        )

        # Mark message as processed
        crud.mark_message_processed(db, db_message.id)

        print(f"✅ Contact form processed successfully from {message.name}: {message.subject}")
        return {
            "status": "success",
            "message": "Your message has been sent successfully! I'll get back to you soon.",
            "message_id": db_message.id
        }

    except ValueError as ve:
        # Configuration error
        print(f"❌ Configuration error: {ve}")
        raise HTTPException(
            status_code=500,
            detail="Email service configuration error. Please contact the administrator."
        )

    except Exception as e:
        # General error
        print(f"❌ Failed to send contact email: {e}")
        raise HTTPException(
            status_code=500,
            detail="Failed to send your message. Please try again later or contact me directly."
        )

@router.post("/chat", response_model=Dict[str, str])
async def chat_endpoint(chat_message: ChatMessage, db: Session = Depends(get_db)):
    logger.info("Chatbot Endpoint accessed")
    reply_text = llm_response(chat_message.message)

    # Store chat log in database
    crud.create_chat_log(
        db=db,
        user_message=chat_message.message,
        bot_response=reply_text
    )

    return {"reply": reply_text}

# Admin endpoints for managing contact messages and chat logs
@router.get("/admin/contact-messages")
def get_contact_messages_endpoint(processed: Optional[bool] = None, db: Session = Depends(get_db)):
    """Get all contact messages (admin endpoint)"""
    messages = crud.get_contact_messages(db, processed=processed)
    return [
        {
            "id": msg.id,
            "name": msg.name,
            "email": msg.email,
            "subject": msg.subject,
            "message": msg.message,
            "created_at": msg.created_at.isoformat(),
            "processed": msg.processed
        }
        for msg in messages
    ]

@router.get("/admin/chat-logs")
def get_chat_logs_endpoint(session_id: Optional[str] = None, db: Session = Depends(get_db)):
    """Get all chat logs (admin endpoint)"""
    logs = crud.get_chat_logs(db, session_id=session_id)
    return [
        {
            "id": log.id,
            "user_message": log.user_message,
            "bot_response": log.bot_response,
            "created_at": log.created_at.isoformat(),
            "session_id": log.session_id
        }
        for log in logs
    ]

@router.put("/admin/contact-messages/{message_id}/processed")
def mark_message_processed_endpoint(message_id: int, db: Session = Depends(get_db)):
    """Mark a contact message as processed (admin endpoint)"""
    message = crud.mark_message_processed(db, message_id)
    if not message:
        raise HTTPException(status_code=404, detail="Message not found")
    return {"success": True, "message": "Message marked as processed"}

@router.post("/chat/stream")
async def chat_stream_endpoint(chat_message: ChatMessage):
    """Stream chat response using Server-Sent Events"""
    logger.info("Chatbot Endpoint accessed")
    async def generate_stream():
        try:
            # Send initial event to indicate streaming started
            yield f"data: {json.dumps({'type': 'start', 'content': ''})}\n\n"

            # Stream the response word by word
            async for word_chunk in llm_stream_response(chat_message.message):
                data = {
                    'type': 'chunk',
                    'content': word_chunk
                }
                yield f"data: {json.dumps(data)}\n\n"

            # Send end event
            yield f"data: {json.dumps({'type': 'end', 'content': ''})}\n\n"

        except Exception as e:
            # Send error event
            error_data = {
                'type': 'error',
                'content': f"Sorry, I couldn't process your message: {str(e)}"
            }
            yield f"data: {json.dumps(error_data)}\n\n"

    return StreamingResponse(
        generate_stream(),
        media_type="text/plain",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
            "Content-Type": "text/event-stream",
        }
    )
