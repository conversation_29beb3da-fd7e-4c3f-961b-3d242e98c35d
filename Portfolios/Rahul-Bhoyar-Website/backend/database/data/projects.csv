title,description,category,date,image_url,id,technologies,live_url,github_url
GraphRAG,"GraphRAG: A scalable Docker-based Retrieval-Augmented Graph system built with LangChain. Integrates knowledge graph embeddings, vector retrieval, and LLM prompts seamlessly. Includes dynamic Docker orchestration, modular components, and clear usage examples.",Generative AI,15/10/2023,https://miro.medium.com/v2/resize:fit:936/0*8h8cHe0EJX5WiKfx.png,1,"LLM, Docker, RAG",,https://github.com/rahulbhoyar1995/GraphRAG
Churn Prediction Using Deep Learning,"Deep learning-based churn prediction model that analyzes customer behavior to identify potential churn. Utilizes neural networks with Keras/TensorFlow for classification. Includes data preprocessing, training, evaluation, and visualization tools.",Deep Learning,01/09/2023,,2,"Python, TensorFlow, React, Pytorch",,https://github.com/rahulbhoyar1995/Churn-Prediction-using-Deep-Learning
Docker Model Runner,"A lightweight Docker-based solution to run and manage machine learning models seamlessly. Ideal for deploying, testing, and scaling ML models in isolated, consistent environments across local and cloud platforms.",Generative AI,20/07/2023,,3,"Docker, Docker Compose, Docker Desktop",,https://github.com/rahulbhoyar1995/Docker-Model-Runner
Global Demographics Dashboard,"An interactive Dash and Plotly app visualizing global demographics with maps and charts. Supports continent and country views, real-time updates, and Docker-based deployment with a beautiful dark-themed UI.",Data Science,10/06/2023,,4,"Python, Pandas, Plotly, Flask, Dash",,https://github.com/rahulbhoyar1995/Global-Demographics-Dashboard
Web Scraping with Crawl4AI,"A lightning-fast, LLM‑friendly web crawler and scraper in Python—outputs clean Markdown, supports JavaScript, structured extraction, browser control, and async processing—ideal for AI pipelines and data workflows.",Generative AI,05/05/2023,,5,"python, web scraping, crawl4ai",,https://github.com/rahulbhoyar1995/Web-Scraping-with-Crawl4AI
Agentic AI with Python,"A Python-based framework for building Agentic AI systems that can reason, plan, and act autonomously. Includes tools for multi-agent coordination, memory, task execution, and integration with LLMs.",Generative AI,15/04/2023,https://d2908q01vomqb2.cloudfront.net/f1f836cb4ea6efb2a0b1b99f41ad8b103eff4b59/2025/03/28/image001-1156x630.png,6,"python, Agents, Langgraph, Langchain, Phidata, MCP, LLMs, CrewAI",,https://github.com/rahulbhoyar1995/AgenticAI-with-Python
Generative AI with Python,"A comprehensive Python repository showcasing Generative AI techniques, including text, image, and data generation. Includes practical examples, model integrations, and deployment workflows for real-world AI applications.",Generative AI,15/04/2023,,7,"python, Langgraph, Langchain, LlamaIndex, VectorDB, RAG, LLMs",,https://github.com/rahulbhoyar1995/GenAI-with-Python
Pydocify,"An LLM-based library that effortlessly auto-generates docstrings for your Python scripts and modules, enhancing code readability with ease.",Generative AI,15/04/2023,,8,"LLM, Python, GenAI",,https://github.com/rahulbhoyar1995/pydocify
LLMs with Ollama Docker,"A Dockerized setup for running Large Language Models (LLMs) locally using Ollama. Easily deploy, manage, and interact with powerful open-source models for AI development, experimentation, and inference tasks.",Generative AI,15/04/2023,,9,"Docker, LLM, Ollama",,https://github.com/rahulbhoyar1995/LLMs-with-Ollama-Docker
AWS With Python,"A comprehensive repository demonstrating how to use AWS services with Python, covering automation, deployment, cloud storage, serverless functions, and SDK usage for real-world cloud development and integration tasks.",Cloud,15/04/2023,https://learnopencv.com/wp-content/uploads/2025/02/Generative-AI.png,10,"Python, AWS, Cloud Computing, Automation",,https://github.com/rahulbhoyar1995/AWS-With-Python
Microsoft Azure with Python,"A practical repository showcasing how to interact with Microsoft Azure services using Python, including automation, resource management, storage, deployment, serverless computing, and SDK-based cloud integration.",Cloud,15/04/2023,,11,"Python, Azure, Cloud Development, Automation",,https://github.com/rahulbhoyar1995/Microsoft-Azure-with-Python
Google Cloud with Python,"A hands-on repository demonstrating how to use Google Cloud services with Python, covering automation, storage, APIs, deployment, serverless functions, and integration using Google Cloud SDK and client libraries.",Cloud,15/04/2023,,12,"Python, Google Cloud, Cloud Computing, DevOps",,https://github.com/rahulbhoyar1995/Google-Cloud-with-Python
Working with Databases with Python,"Explore seamless integration of Python and databases through this repository, offering comprehensive guidance, code examples, and best practices for efficient database interactions and management using Python programming language.",Backend Development,15/04/2023,,13,"Python, Databases, SQLAlchemy",,https://github.com/rahulbhoyar1995/Working-with-Databases-with-Python
Anamoly Detection in Transactions,Detect anomalies in transactional data using advanced statistical methods and machine learning algorithms. Enhance fraud detection and anomaly identification in financial transactions for improved security and risk management.,Machine Learning,15/04/2023,,14,"Python, SKlearn, Jupyter",,https://github.com/rahulbhoyar1995/Anomaly-Detection-in-Transactions
Geospatial Data Analysis Projects,"Explore geospatial data analysis through diverse projects. Gain insights into mapping, visualization, and location-based analytics in this repository focused on practical applications and solutions.",Data Science,15/04/2023,,15,"Python, GeoPandas, Jupyter",,https://github.com/rahulbhoyar1995/Geospatial-Data-Analysis-Projects
Hugging Face Tutorials,A comprehensive repository featuring hands-on tutorials and examples for leveraging HuggingFace NLP models and tools. Perfect for beginners and experts aiming to master state-of-the-art AI technologies.,Deep Learning,15/04/2023,,16,"HuggingFace, Transformers",,https://github.com/rahulbhoyar1995/HuggingFace-Tutorials
Kaggle Data Analysis Projects,"This repository hosts Jupyter files for various Kaggle data analysis projects. Explore datasets, analyze trends, and visualize insights. Delve into diverse topics from Bollywood movies to broader data trends. Dive in for engaging analyses and informative visualizations.",Data Science,15/04/2023,,17,"Data Analysis, Kaggle, Python",www.kaggle.com/rrb8695,https://github.com/rahulbhoyar1995/Kaggle-Data-Analysis-Projects
Car Price Predictions,"This project involves developing a predictive model to estimate car prices. Using a Random Forest Regression model, the system considers various features such as year, present price, kilometers driven, and more to predict and display the potential selling price of a car. The Flask web application facilitates user interaction with the model.",Machine Learning,15/04/2023,,18,"Python, Flask, Machine Learning",,https://github.com/rahulbhoyar1995/car-price-predictions-ml-project
Healthcare Data Analysis,"Conduct comprehensive healthcare data analysis to derive insights, improve patient outcomes, and optimize operational efficiency through advanced statistical methodologies and machine learning techniques. Delivering actionable intelligence for informed decision-making.",Data Science,15/04/2023,,19,"Python, Pandas, Data Analysis",,https://github.com/rahulbhoyar1995/Healthcare-Data-Analysis
Streamlit Templates,"A collection of ready-to-use Streamlit templates for building interactive web apps. Includes dashboards, data visualizations, forms, and custom UI components to accelerate rapid prototyping and app development.",Web Development,15/04/2023,,20,"Python, Streamlit",,https://github.com/rahulbhoyar1995/Streamlit-Templates
Streamlit Tutorials,"A repository containing practical examples and guides for utilising Streamlit, a popular Python library for creating interactive web applications with ease. Explore various tutorials to enhance your data visualization and machine learning projects.",Web Development,15/04/2023,,21,"Python, Streamlit",,https://github.com/rahulbhoyar1995/Streamlit-Tutorials
Deploying Streamlit Apps on Streamlit Cloud,Learn to deploy Python apps effortlessly on Streamlit Community Cloud with this comprehensive tutorial repository. Simplified steps for beginners to get their apps up and running quickly.,DevOps/MLOps,15/04/2023,,22,"Python, Streamlit, Streamlit Cloud",,https://github.com/rahulbhoyar1995/Deploy-Apps-on-Streamlit-Cloud-Tutorial
Python in Containers,"Containerized Python application for seamless development, deployment, and scaling. Leverage Docker to isolate dependencies, improve portability, and ensure consistency across environments in Python-based projects.",Backend Development,15/04/2023,,23,"Docker, Python, Containers",,https://github.com/rahulbhoyar1995/Python-in-Containers
Movies REST APIs Project,"A FastAPI application for accessing and managing movie data, offering various endpoints for searching and retrieving movie information from IMDb dataset.",Backend Development,15/04/2023,,24,"Python, FastAPI, Docker",,https://github.com/rahulbhoyar1995/Movies-APIs-Project
Deep Learning with Python,A robust CI/CD pipeline for automating testing and deployment of applications.,Deep Learning,15/04/2023,,25,"Docker, Jenkins, AWS, Terraform",,https://github.com/yourusername/cicd-pipeline
Bash Scripting and Shell Programming,"Tutorials on writing efficient bash scripts, automating tasks, and mastering Unix/Linux shell commands.",Backend Development,15/04/2023,,26,"Bash, Shell",,https://github.com/rahulbhoyar1995/Bash-Scripting-and-Shell-Programming
Named Entity Recognition : Case Study,"Repositiry contains the code for Case study of ""Named-Entity Recognition"" in Natural Language Processing.",Natural Language Processing,15/04/2023,,27,"NLP, Deep Learning, NER",,https://github.com/rahulbhoyar1995/NER-Case-Study
Natural Language Processing with Python,"A comprehensive repository covering Natural Language Processing with Python, including text preprocessing, sentiment analysis, topic modeling, NER, embeddings, and model building using popular NLP libraries like NLTK, spaCy, and Transformers.",Natural Language Processing,15/04/2023,,28,"Python, NLP, Text Processing, Machine Learning",,https://github.com/rahulbhoyar1995/Natural-Language-Processing-with-Python
Computer Vision with Python,"A practical repository demonstrating Computer Vision with Python, featuring image processing, object detection, classification, facial recognition, and deep learning using OpenCV, TensorFlow, and PyTorch libraries.",Deep Learning,15/04/2023,,29,"Python, Computer Vision, Deep Learning, Image Processing",,https://github.com/rahulbhoyar1995/Computer-Vision-with-Python
Machine Learning with Python,"A complete repository showcasing Machine Learning with Python, including data preprocessing, model training, evaluation, and deployment using libraries like scikit-learn, XGBoost, TensorFlow, and Pandas.",Machine Learning,15/04/2023,https://miro.medium.com/v2/resize:fit:1400/1*cG6U1qstYDijh9bPL42e-Q.jpeg,30,"Python, Machine Learning, AI, Model Training",,https://github.com/rahulbhoyar1995/Machine-Learning-with-Python
Robot Automation Framework,"Explore comprehensive tutorials and examples for mastering Robot Framework, an open-source automation framework. Dive into test automation, keyword-driven testing, and more with practical guidance and hands-on projects.",Backend Development,15/04/2023,,31,"Python, Robot Framework",,https://github.com/rahulbhoyar1995/Robot-Framework-Tutorial
Statistics with Python,"A detailed repository exploring Statistics with Python, covering descriptive stats, probability, hypothesis testing, regression, and visualizations using libraries like NumPy, SciPy, and Matplotlib.",Machine Learning,15/04/2023,,32,"Python, Statistics, Data Analysis, Data Science",,https://github.com/rahulbhoyar1995/Statistics-with-Python
Mathematics with Python,"A focused repository demonstrating core mathematical concepts using Python, including algebra, calculus, linear algebra, discrete math, and numerical methods with libraries like SymPy and NumPy.",Machine Learning,15/04/2023,,33,"Python, Mathematics, Linear Algebra, Numerical Methods",,https://github.com/rahulbhoyar1995/Mathematics-with-Python
MLOPs Tutorials,"A practical repository covering MLOps concepts and workflows, including model tracking, CI/CD, deployment, monitoring, and orchestration using tools like MLflow, Docker, and Kubernetes.",DevOps/MLOps,15/04/2023,,34,"MLOps, Machine Learning, CI/CD, Model Deployment",,https://github.com/rahulbhoyar1995/MLOPs-Tutorials
SQL Tutorials,"A comprehensive repository of SQL tutorials covering queries, joins, subqueries, aggregations, indexing, and database design for effective data extraction and manipulation.",Data Science,15/04/2023,,35,"SQL, Databases, Data Analysis, Querying",,https://github.com/rahulbhoyar1995/SQL-Tutorials
Data Analysis with Python,"A hands-on repository for Data Analysis with Python, featuring data wrangling, cleaning, visualization, and statistical analysis using Pandas, NumPy, Matplotlib, and Seaborn.",Data Science,15/04/2023,,36,"Python, Data Analysis, Pandas, Visualization",,https://github.com/rahulbhoyar1995/Data-Analysis-with-Python
Microsoft Excel Automation,"Automate repetitive tasks and streamline workflows in Microsoft Excel with this project. Enhance efficiency, accuracy, and productivity through custom macros, formulas, and VBA scripting solutions.",Backend Development,15/04/2023,,37,"MS Excel, Automation, Python",,https://github.com/rahulbhoyar1995/MS-Excel-Automation-Project
Pyspark Tutorials,"A complete repository for learning PySpark, covering distributed data processing, DataFrames, SQL, RDDs, and big data analytics using Apache Spark with Python.",Data Engineering,15/04/2023,,38,"PySpark, Big Data, Apache Spark, Data Engineering",,https://github.com/rahulbhoyar1995/Pyspark-Tutorials
Web Development Bootcamp,"An end-to-end web development bootcamp repository covering HTML, CSS, JavaScript, Python, Flask/Django, APIs, and deployment for building responsive, full-stack web applications.",Web Development,15/04/2023,,39,"Web Development, HTML CSS JS, Python, Full Stack",,https://github.com/rahulbhoyar1995/Web-Development
To Do App,"Efficiently manage tasks with this Django-powered to-do app. Seamlessly organize, prioritize, and track your daily activities. Enhance productivity and stay on top of your goals effortlessly. Get started now!",Web Development,15/04/2023,,40,"Python, Django, HTML, CSS",,https://github.com/rahulbhoyar1995/to-do-app
Figma Tutorials,"A beginner-friendly repository for learning Figma, covering UI/UX design, prototyping, components, auto layout, and collaborative design for web and mobile applications",UI/UX Design,15/04/2023,,41,"Figma, UI/UX Design, Prototyping, Product Design",,https://github.com/rahulbhoyar1995/Figma-Tutorials
Web Scraping with Python,"A hands-on repository covering web scraping techniques with Python using libraries like BeautifulSoup, Scrapy, and Selenium to extract and process data from websites.",Backend Development,15/04/2023,,42,"Python, Web Scraping, BeautifulSoup, Automation",,https://github.com/rahulbhoyar1995/Web-Scraping-with-Python
Data Structures and Algorithms in Python,"A structured repository focused on data structures and algorithms in Python, covering arrays, linked lists, trees, sorting, searching, recursion, and problem-solving patterns.",Backend Development,15/04/2023,,43,"Python, DSA, Algorithms, Coding Interview",,https://github.com/rahulbhoyar1995/Data-Structures-and-Algorithms-in-Python
Design Patterns in Python,"A comprehensive repository demonstrating classic design patterns in Python, including creational, structural, and behavioral patterns with real-world examples and best practices.",Backend Development,15/04/2023,,44,"Python, Design Patterns, OOP, Software Architecture",,https://github.com/rahulbhoyar1995/Design-Patterns-in-Python
Expense-Tracker,"Expense-Tracker: Easily manage your finances with this intuitive app. Track expenses, set budgets, and gain insights into your spending habits. Stay financially organized effortlessly.",Web Development,09/07/2025,,45,"Front End, React",,https://github.com/rahulbhoyar1995/Expense-Tracker