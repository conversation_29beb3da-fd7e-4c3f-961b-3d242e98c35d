"""
Database initialization script to create tables and populate with initial data from CSV files.
"""

import os
import csv
import json
import re
from typing import List
import sys

sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from logging_config import setup_logger
from database.connection import create_tables, drop_tables, SessionLocal
from database import crud
logger = setup_logger("database.init_db")

BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, "data")  

def read_csv(filename: str) -> List[dict]:
    filepath = os.path.join(DATA_DIR, filename)
    with open(filepath, newline='', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        rows = []
        for row in reader:
            clean_row = {}
            for key, value in row.items():
                if value is None:
                    clean_row[key] = None
                    continue

                value = value.strip()
                # Convert 'null' or 'none' strings to Python None
                if value.lower() in ['null', 'none']:
                    clean_row[key] = None
                    continue

                # Handle blank values
                if value == '':
                    clean_row[key] = None

                # Handle boolean values
                elif value.lower() == 'true':
                    clean_row[key] = True
                elif value.lower() == 'false':
                    clean_row[key] = False

                # Handle JSON-style lists (proper JSON format)
                elif value.startswith('[') and value.endswith(']'):
                    try:
                        json_value = json.loads(value)
                        clean_row[key] = json_value
                    except json.JSONDecodeError:
                        # If JSON parsing fails, treat as comma-separated string
                        clean_row[key] = parse_comma_separated_field(value, key)

                # Handle comma-separated values for specific JSON fields
                elif key in ['technologies', 'achievements', 'keywords', 'tags']:
                    parsed_value = parse_comma_separated_field(value, key)
                    clean_row[key] = parsed_value

                # Handle integer fields
                elif key in ['level', 'id']:
                    try:
                        clean_row[key] = int(value) if value else None
                    except ValueError:
                        clean_row[key] = None

                else:
                    clean_row[key] = value
            rows.append(clean_row)
        return rows


def parse_comma_separated_field(value: str, field_name: str = None) -> List[str]:
    """
    Parse comma-separated values into a list, handling various formats.
    Special handling for achievements which use sentence-level separation.
    """
    if not value or value.strip() == '':
        return []

    # Remove brackets if present
    value = value.strip()
    if value.startswith('[') and value.endswith(']'):
        value = value[1:-1]

    # Special handling for achievements - split by sentence endings followed by comma
    if field_name == 'achievements':
        # Split by patterns like "., " or ".," to separate achievement sentences
        # Split on comma followed by space and capital letter (new sentence)
        items = re.split(r',\s+(?=[A-Z])', value)
        return [item.strip() for item in items if item.strip()]

    # For other fields (technologies, keywords, tags), split by comma
    items = []
    for item in value.split(','):
        item = item.strip()
        # Remove quotes if present
        if item.startswith('"') and item.endswith('"'):
            item = item[1:-1]
        elif item.startswith("'") and item.endswith("'"):
            item = item[1:-1]

        if item:  # Only add non-empty items
            items.append(item)

    return items



def init_database():
    print("🔄 Initializing database...")

    create_tables()
    print("✅ Database tables created successfully!")

    db = SessionLocal()

    try:
        # Seed Skills
        print("  📊 Adding skills...")
        skills_data = read_csv("skills.csv")
        for i, row in enumerate(skills_data):
            try:
                crud.create_or_update_skill(db, **row)
            except Exception as e:
                db.rollback()
                print(f"    ❌ Error adding skill row {i+1}: {e}")
                print(f"    📋 Row data: {row}")

        # Seed Experiences
        print("  💼 Adding experiences...")
        experiences_data = read_csv("experiences.csv")
        for i, row in enumerate(experiences_data):
            try:
                crud.create_or_update_experience(db, **row)
            except Exception as e:
                db.rollback()
                print(f"    ❌ Error adding experience row {i+1}: {e}")
                print(f"    📋 Row data: {row}")

        # Seed Education
        print("  🎓 Adding education...")
        education_data = read_csv("education.csv")
        for i, row in enumerate(education_data):
            try:
                crud.create_or_update_education(db, **row)
            except Exception as e:
                db.rollback()
                print(f"    ❌ Error adding education row {i+1}: {e}")
                print(f"    📋 Row data: {row}")

        # Seed Certifications
        print("  🏆 Adding certifications...")
        certifications_data = read_csv("certifications.csv")
        for i, row in enumerate(certifications_data):
            try:
                crud.create_or_update_certification(db, **row)
            except Exception as e:
                db.rollback()
                print(f"    ❌ Error adding certification row {i+1}: {e}")
                print(f"    📋 Row data: {row}")

        # Seed Projects
        print("  🚀 Adding projects...")
        projects_data = read_csv("projects.csv")
        for i, row in enumerate(projects_data):
            try:
                crud.create_or_update_project(db, **row)
            except Exception as e:
                db.rollback()
                print(f"    ❌ Error adding project row {i+1}: {e}")
                print(f"    📋 Row data: {row}")

        # Seed Publications
        print("  📚 Adding publications...")
        publications_data = read_csv("publications.csv")
        for i, row in enumerate(publications_data):
            try:
                crud.create_or_update_publication(db, **row)
            except Exception as e:
                db.rollback()
                print(f"    ❌ Error adding publication row {i+1}: {e}")
                print(f"    📋 Row data: {row}")

        print("✅ Database seeded successfully!")

    except Exception as e:
        print(f"❌ Error seeding database: {e}")
        db.rollback()
        raise
    finally:
        db.close()


def reset_database():
    try:
        logger.info("🔄 Resetting the database...")
        drop_tables()
        init_database()
        logger.info("✅ Database reset complete.")
    except Exception as e:
        logger.exception(f"❌ Failed to reset database: {e}")


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="Database initialization script")
    parser.add_argument("--reset", action="store_true", help="Reset the database")
    args = parser.parse_args()

    if args.reset:
        reset_database()
    else:
        init_database()
