import React from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON><PERSON> } from 'react-bootstrap';
import {
  FaGithub, FaExternalLinkAlt, FaCode, FaServer, FaMobileAlt,
  FaReact, FaPython, FaDatabase, FaDocker, FaAws, FaNodeJs
} from 'react-icons/fa';
import './ProjectCard.css';

const ProjectCard = ({ project }) => {
  // Function to get an icon based on the project's technologies
  const getProjectIcon = (technologies) => {
    const techLower = technologies.map(tech => tech.toLowerCase());

    if (techLower.some(tech => tech.includes('react'))) {
      return <FaReact className="project-icon" />;
    } else if (techLower.some(tech => tech.includes('node'))) {
      return <FaNodeJs className="project-icon" />;
    } else if (techLower.some(tech => tech.includes('python') || tech.includes('django') || tech.includes('flask') || tech.includes('fastapi'))) {
      return <FaPython className="project-icon" />;
    } else if (techLower.some(tech => tech.includes('database') || tech.includes('sql') || tech.includes('mongo'))) {
      return <FaDatabase className="project-icon" />;
    } else if (techLower.some(tech => tech.includes('docker') || tech.includes('container'))) {
      return <FaDocker className="project-icon" />;
    } else if (techLower.some(tech => tech.includes('aws') || tech.includes('cloud'))) {
      return <FaAws className="project-icon" />;
    } else if (techLower.some(tech => tech.includes('api') || tech.includes('server') || tech.includes('backend'))) {
      return <FaServer className="project-icon" />;
    } else if (techLower.some(tech => tech.includes('frontend') || tech.includes('web'))) {
      return <FaCode className="project-icon" />;
    } else {
      return <FaMobileAlt className="project-icon" />;
    }
  };

  return (
    <Card className="h-100 project-card">
      <div
        className={`project-header ${project.image_url ? 'has-background-image' : ''}`}
        style={project.image_url ? {
          backgroundImage: `url(${project.image_url})`,
          backgroundSize: 'cover',
          backgroundPosition: 'center',
          backgroundRepeat: 'no-repeat'
        } : {}}
      >
        {!project.image_url && getProjectIcon(project.technologies)}
        {project.image_url && (
          <div className="image-overlay">
            <div className="project-category-badge">
              {project.category}
            </div>
          </div>
        )}
      </div>
      <Card.Body>
        <Card.Title>{project.title}</Card.Title>
        <Card.Text>{project.description}</Card.Text>
        <div className="mb-3">
          {project.technologies.map((tech, index) => (
            <Badge bg="secondary" className="tech-badge me-2 mb-2" key={index}>
              {tech}
            </Badge>
          ))}
        </div>
      </Card.Body>
      <Card.Footer>
        {project.github_url && (
          <Button
            variant="outline-dark"
            href={project.github_url}
            target="_blank"
            rel="noopener noreferrer"
            size="sm"
          >
            <FaGithub className="me-2" /> GitHub
          </Button>
        )}
        {project.live_url && (
          <Button
            variant="primary"
            href={project.live_url}
            target="_blank"
            rel="noopener noreferrer"
            size="sm"
          >
            <FaExternalLinkAlt className="me-2" /> Live Demo
          </Button>
        )}
      </Card.Footer>
    </Card>
  );
};

export default ProjectCard;
