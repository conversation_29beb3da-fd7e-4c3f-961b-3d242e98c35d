import React, { useState, useEffect } from 'react';
import { Container, Row, Col, Form, InputGroup } from 'react-bootstrap';
import { FaSearch, FaExternalLinkAlt, FaFilePdf, FaQuoteLeft } from 'react-icons/fa';
import axios from 'axios';
import './Publications.css';

const Publications = () => {
  const [publications, setPublications] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('All');

  // Categories for filtering
  const categories = [
    'All',
    'Generative AI',
    'Machine Learning'
  ];

  useEffect(() => {
    const fetchPublications = async () => {
      try {
        const response = await axios.get('/api/publications');
        setPublications(response.data);
        setLoading(false);
      } catch (err) {
        console.error('Error fetching publications:', err);
        setError('Failed to load publications. Please try again later.');
        setLoading(false);
      }
    };

    fetchPublications();
  }, []);

  // Filter publications based on search term and category
  const filteredPublications = publications.filter(publication => {
    const matchesSearch =
      publication.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      publication.abstract.toLowerCase().includes(searchTerm.toLowerCase()) ||
      publication.authors.toLowerCase().includes(searchTerm.toLowerCase()) ||
      publication.keywords.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory = selectedCategory === 'All' || publication.category === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  // Featured publications
  const featuredPublications = publications.filter(publication => publication.featured);

  if (loading) {
    return (
      <Container className="py-5 mt-5">
        <div className="text-center">
          <div className="spinner-border text-primary" role="status">
            <span className="visually-hidden">Loading...</span>
          </div>
          <p className="mt-3">Loading publications...</p>
        </div>
      </Container>
    );
  }

  if (error) {
    return (
      <Container className="py-5 mt-5">
        <div className="alert alert-danger" role="alert">
          {error}
        </div>
      </Container>
    );
  }

  return (
    <div className="publications-page">
      <section className="publications-hero">
        <Container>
          <Row className="justify-content-center">
            <Col lg={8} className="text-center">
              <h1 className="publications-title">My Publications</h1>
              <p className="publications-subtitle">
                Research papers and articles published in academic journals and conferences
              </p>
              <InputGroup className="search-bar">
                <InputGroup.Text>
                  <FaSearch />
                </InputGroup.Text>
                <Form.Control
                  placeholder="Search publications..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </InputGroup>
            </Col>
          </Row>
        </Container>
      </section>

      {searchTerm === '' && selectedCategory === 'All' && (
        <section className="featured-publications">
          <Container>
            <h2 className="section-title text-center">Featured Publications</h2>
            <div className="section-intro text-center mb-5">
              <p className="lead">
                Highlighted research work with significant impact in the field
              </p>
            </div>
            <Row>
              {featuredPublications.map(publication => (
                <Col lg={6} key={publication.id} className="mb-4">
                  <div className="publication-card featured">
                    <div className="publication-header text-left">
                      <span className="publication-year">{publication.year}</span>
                      <h3 className="publication-title">{publication.title}</h3>
                      <p className="publication-authors">{publication.authors}</p>
                      <p className="publication-journal">
                        <em>{publication.journal}</em>
                      </p>
                    </div>
                    <div className="publication-body text-left">
                      <div className="publication-abstract">
                        <FaQuoteLeft className="quote-icon" />
                        <p>{publication.abstract.substring(0, 200)}...</p>
                      </div>
                      <div className="publication-keywords">
                        {publication.keywords.map((keyword, index) => (
                          <span key={index} className="publication-keyword">{keyword}</span>
                        ))}
                      </div>
                      <div className="publication-links">
                        {publication.pdf_link && (
                          publication.published ? (
                            <a href={publication.pdf_link} className="publication-link pdf" target="_blank" rel="noopener noreferrer">
                              <FaFilePdf /> PDF
                            </a>
                          ) : (
                            <span
                              className="publication-link pdf disabled"
                              data-tooltip="Paper is yet to published"
                            >
                              <FaFilePdf /> PDF
                            </span>
                          )
                        )}
                        {publication.external_link && (
                          <a href={publication.external_link} className="publication-link external" target="_blank" rel="noopener noreferrer">
                            <FaExternalLinkAlt /> DOI
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                </Col>
              ))}
            </Row>
          </Container>
        </section>
      )}

      <section className="all-publications">
        <Container>
          <Row>
            <Col lg={9}>
              <div className="publications-header text-left">
                <h2 className="section-title">
                  {searchTerm !== '' ? 'Search Results' :
                   selectedCategory !== 'All' ? `${selectedCategory} Publications` : 'All Publications'}
                </h2>
                <div className="publications-count">{filteredPublications.length} publications</div>
              </div>

              <div className="publications-list">
                {filteredPublications.map(publication => (
                  <div key={publication.id} className="publication-card">
                    <div className="publication-header text-left">
                      <span className="publication-year">{publication.year}</span>
                      <h3 className="publication-title">{publication.title}</h3>
                      <p className="publication-authors">{publication.authors}</p>
                      <p className="publication-journal">
                        <em>{publication.journal}</em>
                      </p>
                    </div>
                    <div className="publication-body text-left">
                      <div className="publication-abstract">
                        <FaQuoteLeft className="quote-icon" />
                        <p>{publication.abstract.substring(0, 200)}...</p>
                      </div>
                      <div className="publication-keywords">
                        {publication.keywords.map((keyword, index) => (
                          <span key={index} className="publication-keyword">{keyword}</span>
                        ))}
                      </div>
                      <div className="publication-links">
                        {publication.pdf_link && (
                          publication.published ? (
                            <a href={publication.pdf_link} className="publication-link pdf" target="_blank" rel="noopener noreferrer">
                              <FaFilePdf /> PDF
                            </a>
                          ) : (
                            <span
                              className="publication-link pdf disabled"
                              data-tooltip="Paper is yet to published"
                            >
                              <FaFilePdf /> PDF
                            </span>
                          )
                        )}
                        {publication.external_link && (
                          <a href={publication.external_link} className="publication-link external" target="_blank" rel="noopener noreferrer">
                            <FaExternalLinkAlt /> DOI
                          </a>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {filteredPublications.length === 0 && (
                <div className="no-results">
                  <h3>No publications found</h3>
                  <p>Try adjusting your search or filter criteria.</p>
                </div>
              )}
            </Col>

            <Col lg={3}>
              <div className="publications-sidebar text-left">
                <div className="sidebar-widget">
                  <h4 className="widget-title">Categories</h4>
                  <ul className="category-list">
                    {categories.map((category, index) => (
                      <li
                        key={index}
                        className={selectedCategory === category ? 'active' : ''}
                        onClick={() => setSelectedCategory(category)}
                      >
                        {category}
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="sidebar-widget">
                  <h4 className="widget-title">Citation Metrics</h4>
                  <div className="metrics-list">
                    <div className="metric-item">
                      <span className="metric-value">{publications.length}</span>
                      <span className="metric-label">Publications</span>
                    </div>
                    <div className="metric-item">
                      <span className="metric-value">312</span>
                      <span className="metric-label">Citations</span>
                    </div>
                    <div className="metric-item">
                      <span className="metric-value">8</span>
                      <span className="metric-label">h-index</span>
                    </div>
                  </div>
                </div>
              </div>
            </Col>
          </Row>
        </Container>
      </section>
    </div>
  );
};

export default Publications;
